{"db_name": "PostgreSQL", "query": "SELECT id, name, item_id FROM item_detail WHERE item_id = $1", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int4"}, {"ordinal": 1, "name": "name", "type_info": "Text"}, {"ordinal": 2, "name": "item_id", "type_info": "Int4"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false]}, "hash": "afcd03053805520201d6feb9610c9b77a4a3b1311d9bed42e6aea8f4a5981b67"}