-- --
-- -- TOC entry 215 (class 1259 OID 16385)
-- -- Name: item; Type: TABLE; Schema: public; Owner: apiuser
-- --

CREATE TABLE public.item (
    id integer NOT NULL,
    name text NOT NULL
);


-- ALTER TABLE public.item OWNER TO apiuser;

-- --
-- -- TOC entry 216 (class 1259 OID 16390)
-- -- Name: item_detail; Type: TABLE; Schema: public; Owner: apiuser
-- --

CREATE TABLE public.item_detail (
    id integer NOT NULL,
    name text NOT NULL,
    item_id integer NOT NULL
);


-- ALTER TABLE public.item_detail OWNER TO apiuser;

-- --
-- -- TOC entry 217 (class 1259 OID 16395)
-- -- Name: item_detail_id_seq; Type: SEQUENCE; Schema: public; Owner: apiuser
-- --

-- CREATE SEQUENCE public.item_detail_id_seq
--     START WITH 1
--     INCREMENT BY 1
--     NO MINVALUE
--     NO MAXVALUE
--     CACHE 1;


-- ALTER SEQUENCE public.item_detail_id_seq OWNER TO apiuser;

-- --
-- -- TOC entry 218 (class 1259 OID 16396)
-- -- Name: item_detail_id_seq1; Type: SEQUENCE; Schema: public; Owner: apiuser
-- --

CREATE SEQUENCE public.item_detail_id_seq1
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


-- ALTER SEQUENCE public.item_detail_id_seq1 OWNER TO apiuser;

-- --
-- -- TOC entry 3372 (class 0 OID 0)
-- -- Dependencies: 218
-- -- Name: item_detail_id_seq1; Type: SEQUENCE OWNED BY; Schema: public; Owner: apiuser
-- --

ALTER SEQUENCE public.item_detail_id_seq1 OWNED BY public.item_detail.id;


-- --
-- -- TOC entry 219 (class 1259 OID 16397)
-- -- Name: item_detail_item_id_seq; Type: SEQUENCE; Schema: public; Owner: apiuser
-- --

-- CREATE SEQUENCE public.item_detail_item_id_seq
--     AS integer
--     START WITH 1
--     INCREMENT BY 1
--     NO MINVALUE
--     NO MAXVALUE
--     CACHE 1;


-- ALTER SEQUENCE public.item_detail_item_id_seq OWNER TO apiuser;

-- --
-- -- TOC entry 3373 (class 0 OID 0)
-- -- Dependencies: 219
-- -- Name: item_detail_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: apiuser
-- --

-- ALTER SEQUENCE public.item_detail_item_id_seq OWNED BY public.item_detail.item_id;


-- --
-- -- TOC entry 220 (class 1259 OID 16398)
-- -- Name: item_id_seq; Type: SEQUENCE; Schema: public; Owner: apiuser
-- --

CREATE SEQUENCE public.item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


-- ALTER SEQUENCE public.item_id_seq OWNER TO apiuser;

-- --
-- -- TOC entry 3374 (class 0 OID 0)
-- -- Dependencies: 220
-- -- Name: item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: apiuser
-- --

ALTER SEQUENCE public.item_id_seq OWNED BY public.item.id;


-- --
-- -- TOC entry 3210 (class 2604 OID 16399)
-- -- Name: item id; Type: DEFAULT; Schema: public; Owner: apiuser
-- --

ALTER TABLE ONLY public.item ALTER COLUMN id SET DEFAULT nextval('public.item_id_seq'::regclass);


-- --
-- -- TOC entry 3211 (class 2604 OID 16400)
-- -- Name: item_detail id; Type: DEFAULT; Schema: public; Owner: apiuser
-- --

ALTER TABLE ONLY public.item_detail ALTER COLUMN id SET DEFAULT nextval('public.item_detail_id_seq1'::regclass);


-- --
-- -- TOC entry 3360 (class 0 OID 16385)
-- -- Dependencies: 215
-- -- Data for Name: item; Type: TABLE DATA; Schema: public; Owner: apiuser
-- --

-- COPY public.item (id, name) FROM stdin;
-- 1	Bűnüldözési FŐOSZTÁLY
-- 3	Bűnüldözési FŐOSZTÁLY
-- 4	Bűnüldözési FŐOSZTÁLY
-- 2	Lőrincügyi Főorbán
-- 5	Csörmős Küküllő
-- \.


-- --
-- -- TOC entry 3361 (class 0 OID 16390)
-- -- Dependencies: 216
-- -- Data for Name: item_detail; Type: TABLE DATA; Schema: public; Owner: apiuser
-- --

-- COPY public.item_detail (id, name, item_id) FROM stdin;
-- 1	Ződölgő BŐRHŰYŐ 1	1
-- 2	Ződölgő BŐRHŰYŐ 2	1
-- 3	Ződölgő BŐRHŰYŐ 3	1
-- 4	Ződölgő BŐRHŰYŐ 4	1
-- \.


-- --
-- -- TOC entry 3375 (class 0 OID 0)
-- -- Dependencies: 217
-- -- Name: item_detail_id_seq; Type: SEQUENCE SET; Schema: public; Owner: apiuser
-- --

-- SELECT pg_catalog.setval('public.item_detail_id_seq', 1, false);


-- --
-- -- TOC entry 3376 (class 0 OID 0)
-- -- Dependencies: 218
-- -- Name: item_detail_id_seq1; Type: SEQUENCE SET; Schema: public; Owner: apiuser
-- --

-- SELECT pg_catalog.setval('public.item_detail_id_seq1', 4, true);


-- --
-- -- TOC entry 3377 (class 0 OID 0)
-- -- Dependencies: 219
-- -- Name: item_detail_item_id_seq; Type: SEQUENCE SET; Schema: public; Owner: apiuser
-- --

-- SELECT pg_catalog.setval('public.item_detail_item_id_seq', 1, false);


-- --
-- -- TOC entry 3378 (class 0 OID 0)
-- -- Dependencies: 220
-- -- Name: item_id_seq; Type: SEQUENCE SET; Schema: public; Owner: apiuser
-- --

-- SELECT pg_catalog.setval('public.item_id_seq', 5, true);


-- --
-- -- TOC entry 3215 (class 2606 OID 16402)
-- -- Name: item_detail item_detail_pk; Type: CONSTRAINT; Schema: public; Owner: apiuser
-- --

ALTER TABLE ONLY public.item_detail
    ADD CONSTRAINT item_detail_pk PRIMARY KEY (id);


-- --
-- -- TOC entry 3213 (class 2606 OID 16404)
-- -- Name: item item_pkey; Type: CONSTRAINT; Schema: public; Owner: apiuser
-- --

ALTER TABLE ONLY public.item
    ADD CONSTRAINT item_pkey PRIMARY KEY (id);


-- --
-- -- TOC entry 3216 (class 2606 OID 16405)
-- -- Name: item_detail item_detail_item_fk; Type: FK CONSTRAINT; Schema: public; Owner: apiuser
-- --

ALTER TABLE ONLY public.item_detail
    ADD CONSTRAINT item_detail_item_fk FOREIGN KEY (item_id) REFERENCES public.item(id) ON UPDATE CASCADE ON DELETE CASCADE;

insert into item (name) values ('Mirgációból létrehozott fűrészpörköltök');

