use crate::orm::{
    item_detail::ItemDetail,
    model::{Model, OrmError},
    set_option::SetOption,
};
use builder_pattern::Builder;
use serde::{Deserialize, Serialize};

#[derive(sqlx::FromRow, Debug, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct Item {
    pub id: Option<i32>,
    pub name: String,
}

#[derive(<PERSON>uild<PERSON>, Debug, Clone)]
pub struct ItemCreate {
    #[default(SetOption::NotSet)]
    pub name: SetOption<String>,
}

#[derive(Builder, Debug, Clone)]
pub struct ItemUpdate {
    #[default(SetOption::NotSet)]
    pub id: SetOption<i32>,

    #[default(SetOption::NotSet)]
    pub name: SetOption<String>,
}

impl Model for ItemCreate {}

impl ItemCreate {
    pub async fn save(&self, pool: &sqlx::Pool<sqlx::Postgres>) -> Result<Item, OrmError> {
        let name = self
            .name
            .value()
            .map_err(|e| OrmError::new("Name not set"))?;

        let trimmed_name = Self::trim(name);

        // todo: 💥💥💥 tobb mezonel querybuilder
        // let mut query = sqlx::QueryBuilder::new("insert into item UPDATE users SET updated_at = ");

        sqlx::query_as!(
            Item,
            "INSERT INTO item (name) VALUES ($1) RETURNING id, name",
            trimmed_name
        )
        .fetch_one(pool)
        .await
        .map_err(|e| OrmError::new("Error saving item."))
    }
}

impl Model for ItemUpdate {}

impl ItemUpdate {
    pub async fn save(&self, pool: &sqlx::Pool<sqlx::Postgres>) -> Result<Option<Item>, OrmError> {
        let name = self
            .name
            .value()
            .map_err(|e| OrmError::new("Name not set."))?;

        let trimmed_name = Self::trim(name);
        let id = self
            .id
            .value()
            .map_err(|e| OrmError::new("Id not set."))?;

        sqlx::query_as!(
            Item,
            "UPDATE item SET name = $1 WHERE id = $2 RETURNING id, name",
            trimmed_name,
            id
        )
        .fetch_optional(pool)
        .await
        .map_err(|e| OrmError::with_cause("Error updating item.", &e.to_string()))
    }
}

impl Item {
    pub async fn find(
        pool: &sqlx::Pool<sqlx::Postgres>,
        id: i32,
    ) -> Result<Option<Self>, OrmError> {
        sqlx::query_as!(Item, "SELECT id, name FROM item WHERE id = $1", id)
            .fetch_optional(pool)
            .await
            .map_err(|e| OrmError::with_cause("Error finding item.", &e.to_string()))
    }

    pub async fn find_all(
        pool: &sqlx::Pool<sqlx::Postgres>,
    ) -> Result<Vec<Self>, OrmError> {
        // Err(anyhow::anyhow!("Zsafol a ebseggbe"))

        sqlx::query_as!(Item, "SELECT id, name FROM item")
            .fetch_all(pool)
            .await
            .map_err(|e| OrmError::with_cause("Error finding items.", &e.to_string()))
    }

    pub async fn get_details(
        &self,
        pool: &sqlx::Pool<sqlx::Postgres>,
    ) -> Result<Vec<ItemDetail>, OrmError> {
        sqlx::query_as!(
            ItemDetail,
            "SELECT id, name, item_id FROM item_detail WHERE item_id = $1",
            self.id.unwrap()
        )
        .fetch_all(pool)
        .await
        .map_err(|e| OrmError::with_cause("Error finding item details.", &e.to_string()))
    }
}
