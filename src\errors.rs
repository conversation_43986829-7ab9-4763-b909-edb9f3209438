use actix_web::{HttpResponse, error::ResponseError, http::StatusCode};
use serde::Serialize;

#[derive(Debug)]
pub enum AppErrorType {
    Database,
    NotFound,
    BadRequest,
    InternalServer,
    Unauthorized,
}

#[derive(Debug)]
pub struct AppError {
    pub message: Option<String>,
    pub cause: Option<String>,
    pub error_type: AppErrorType,
}

impl AppError {
    fn message(&self) -> String {
        match &*self {
            AppError {
                message: Some(message),
                cause: _,
                error_type: _,
            } => message.clone(),
            AppError {
                message: None,
                cause: _,
                error_type: AppErrorType::NotFound,
            } => "The requested item was not found.".to_string(),
            AppError {
                message: None,
                cause: _,
                error_type: AppErrorType::BadRequest,
            } => "Bad request".to_string(),
            AppError {
                message: None,
                cause: _,
                error_type: AppErrorType::InternalServer,
            } => "Internal server error".to_string(),
            AppError {
                message: None,
                cause: _,
                error_type: AppErrorType::Unauthorized,
            } => "Unauthorized".to_string(),
            _ => "Unknown error".to_string(),
        }
    }
}

impl std::fmt::Display for AppError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}

#[derive(Serialize)]
pub struct AppErrorResponse {
    pub error: String,
}

impl ResponseError for AppError {
    fn status_code(&self) -> StatusCode {
        match self.error_type {
            AppErrorType::Database => StatusCode::INTERNAL_SERVER_ERROR,
            AppErrorType::NotFound => StatusCode::NOT_FOUND,
            AppErrorType::BadRequest => StatusCode::BAD_REQUEST,
            AppErrorType::InternalServer => StatusCode::INTERNAL_SERVER_ERROR,
            AppErrorType::Unauthorized => StatusCode::UNAUTHORIZED,
        }
    }

    fn error_response(&self) -> HttpResponse {
        actix_web::HttpResponse::build(self.status_code()).json(AppErrorResponse {
            error: self.message(),
        })
    }
}
