use actix_web::{
    App, Error, HttpResponse, HttpServer,
    dev::{ServiceRequest, ServiceResponse},
    get,
    middleware::{ErrorHandlerResponse, ErrorHandlers, Logger},
    post, web,
};
use actix_web_httpauth::{extractors::bearer::<PERSON><PERSON><PERSON><PERSON>, middleware::HttpAuthentication};
use dotenvy::dotenv;
use sqlx::{Pool, Postgres, postgres::PgPoolOptions};
use std::{env, time::Duration};

use crate::{
    errors::AppError,
    orm::{
        item::{Item, ItemCreate, ItemUpdate},
        set_option::SetOption::Set,
    },
};

use tracing_subscriber::{fmt::{self, fmt}, layer::SubscriberExt};
use tracing_subscriber::filter::EnvFilter;

mod errors;
mod orm;

struct AppState {
    pool: Pool<Postgres>,
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();

    tracing_subscriber::registry()
        .with(fmt::layer())
        .with(EnvFilter::from_default_env())
        .init();

    let pool = PgPoolOptions::new()
        .acquire_timeout(Duration::from_secs(5))
        .connect(&env::var("DATABASE_URL").unwrap())
        .await
        .unwrap();

    sqlx::migrate!("./migrations").run(&pool).await.unwrap();

    let auth = HttpAuthentication::bearer(validator);

    let app_state = web::Data::new(AppState { pool });

    HttpServer::new(move || {
        App::new()
            .wrap(auth.clone())
            .wrap(Logger::default())
            .wrap(ErrorHandlers::new().handler(
                actix_web::http::StatusCode::INTERNAL_SERVER_ERROR,
                error_handler,
            ))
            .app_data(app_state.clone())
            .service(get_items)
            .service(get_item)
            .service(create_item)
            .service(update_item)
            .service(get_item_details)
    })
    .bind(("0.0.0.0", env::var("WEB_PORT").unwrap().parse().unwrap()))?
    .run()
    .await
}

#[post("/item")]
async fn create_item(
    app_state: web::Data<AppState>,
    item: web::Json<Item>,
) -> Result<HttpResponse, Error> {
    let new_item = ItemCreate::new().name(Set(item.name.clone())).build();

    let item = new_item
        .save(&app_state.pool)
        .await
        .map_err(|err| AppError::from(err))?;

    Ok(HttpResponse::Ok().json(item))
}

#[get("/items")]
async fn get_items(app_state: web::Data<AppState>) -> Result<HttpResponse, Error> {
    let items = Item::find_all(&app_state.pool)
        .await
        .map_err(|err| AppError::from(err))?;

    // Err(actix_web::error::ErrorInternalServerError("Not implemented"))
    Ok(HttpResponse::Ok().json(items))
}

#[get("/item/{id}")]
async fn get_item(
    app_state: web::Data<AppState>,
    id: web::Path<String>,
) -> Result<HttpResponse, Error> {
    let id_string = id.into_inner();
    let id_int = id_string.parse::<i32>();

    match id_int {
        Ok(id_int) => {
            let item = Item::find(&app_state.pool, id_int)
                .await
                .map_err(|err| AppError::from(err))?;

            match item {
                Some(item) => Ok(HttpResponse::Ok().json(item)),
                None => Err(actix_web::error::ErrorNotFound("Item not found")),
            }
        }
        Err(_) => Err(actix_web::error::ErrorBadRequest("Invalid id")),
    }
}

#[post("/item/{id}")]
async fn update_item(
    app_state: web::Data<AppState>,
    id: web::Path<String>,
    item: web::Json<Item>,
) -> Result<HttpResponse, Error> {
    let id_string = id.into_inner();
    let id_int = id_string.parse::<i32>();

    match id_int {
        Ok(id_int) => {
            let upd_item = ItemUpdate::new()
                .id(Set(id_int))
                .name(Set(item.name.clone()))
                .build();

            let item = upd_item
                .save(&app_state.pool)
                .await
                .map_err(actix_web::error::ErrorInternalServerError)?;

            match item {
                Some(item) => Ok(HttpResponse::Ok().json(item)),
                None => Err(actix_web::error::ErrorNotFound("Item not found")),
            }
        }
        Err(_) => Err(actix_web::error::ErrorBadRequest("Invalid id")),
    }
}

#[get("/item/{id}/details")]
async fn get_item_details(
    app_state: web::Data<AppState>,
    id: web::Path<String>,
) -> Result<HttpResponse, Error> {
    let id_string = id.into_inner();
    let id_int = id_string.parse::<i32>();

    match id_int {
        Ok(id_int) => {
            let item = Item::find(&app_state.pool, id_int)
                .await
                .map_err(actix_web::error::ErrorInternalServerError)?;

            match item {
                Some(item) => Ok(HttpResponse::Ok().json(
                    item.get_details(&app_state.pool)
                        .await
                        .map_err(actix_web::error::ErrorInternalServerError)?,
                )),
                None => Err(actix_web::error::ErrorNotFound("Item not found")),
            }
        }
        Err(_) => Err(actix_web::error::ErrorBadRequest("Invalid id")),
    }
}

async fn validator(
    req: ServiceRequest,
    credentials: BearerAuth,
) -> Result<ServiceRequest, (Error, ServiceRequest)> {
    if credentials.token() == "mysecrettoken" {
        Ok(req)
    } else {
        Err((actix_web::error::ErrorUnauthorized("Invalid token"), req))
    }
}

fn error_handler<B>(res: ServiceResponse<B>) -> actix_web::Result<ErrorHandlerResponse<B>> {
    eprintln!("Error: {}", res.response().error().unwrap());
    Ok(ErrorHandlerResponse::Response(res.map_into_left_body()))
}

/*
Migraciok helyrehozasa, ha rossz a checksum>

cargo sqlx migrate info

UPDATE _sqlx_migrations sm
SET checksum = decode('3b059c673974a3e596431be74736d5be7a8a4fb8ea961ced440620ffc834e487abd7a458be21be13f696c436986d89e3', 'hex')
WHERE version = 202506272139;

 */
