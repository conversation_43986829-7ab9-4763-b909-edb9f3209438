use std::{error::Error, fmt};

#[derive (Debug)]
pub struct SetOptionError;

impl fmt::Display for SetOptionError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SetOptionError")
    }
}

impl Error for SetOptionError {}

// SetOption util
#[derive(Debug, Clone, Default, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum SetOption<T> {
    Set(T),
    #[default]
    NotSet,
}

/// Implement `From` for `SetOption` to allow for easy conversion from a value to a `SetOption`.
/// Mostly used by the `[into]` attribute of the builder-pattern macros
/// ```
/// let set_option: SetOption<i32> = 1.into();
/// assert_eq!(set_option, SetOption::Set(1));
/// ```
impl<T> From<T> for SetOption<T> {
    fn from(value: T) -> Self {
        SetOption::Set(value)
    }
}

impl<T> SetOption<T> {
    pub fn value(&self) -> Result<&T, SetOptionError> {
        match self {
            SetOption::Set(value) => Ok(value),
            SetOption::NotSet => Err(SetOptionError {}),
        }
    }
    // pub fn is_set(&self) -> bool {
    //     match self {
    //         SetOption::Set(_) => true,
    //         SetOption::NotSet => false,
    //     }
    // }

    // pub fn is_not_set(&self) -> bool {
    //     match self {
    //         SetOption::Set(_) => false,
    //         SetOption::NotSet => true,
    //     }
    // }
}

impl<T> From<Option<T>> for SetOption<T> {
    fn from(value: Option<T>) -> Self {
        match value {
            Some(value) => SetOption::Set(value),
            None => SetOption::NotSet,
        }
    }
}
