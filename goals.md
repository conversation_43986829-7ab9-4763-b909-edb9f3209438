# Célok a projektben

1. Rust alkalmazás
2. Levelezés
3. Távoli api elérése
4. Hibakezelés
5. Docker
6. Eseménylogolás
7. Tesztelés
8. Struktúrálás
9. Health check, riasztás
10. HTTPS
11. Deployment




Sendgrid:
<EMAIL>
export SENDGRID_API_KEY='*********************************************************************'

  --url https://api.eu.sendgrid.com/v3/mail/send \

curl --request POST \
  --url https://api.sendgrid.com/v3/mail/send \
  --header "Authorization: Bearer *********************************************************************" \
  --header 'Content-Type: application/json' \
  --data '{"personalizations": [{"to": [{"email": "<EMAIL>"}]}],"from": {"email": "<EMAIL>", "name": "Boszorkánysziget"},"subject": "Sending with SendGrid is Fun","content": [{"type": "text/plain", "value": "and easy to do anywhere, even with cURL"}]}'
