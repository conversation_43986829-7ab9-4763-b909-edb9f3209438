use std::{error::Error, fmt::Display};

use crate::errors::{AppError, AppErrorType};

pub trait Model {
    fn trim(s: &str) -> String {
        s.trim().to_string()
    }
}

#[derive(Debug)]
pub struct OrmError {
    message: String,
    cause: Option<String>,
}

impl Display for OrmError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}

impl Error for OrmError {}

impl From<sqlx::Error> for OrmError {
    fn from(value: sqlx::Error) -> Self {
        OrmError {
            message: format!("SQLX error: {}", value),
            cause: Some(value.to_string()),
        }
    }
}

impl OrmError {
    pub fn new(message: &str) -> Self {
        OrmError {
            message: message.to_string(),
            cause: None,
        }
    }

    pub fn with_cause(message: &str, cause: &str) -> Self {
        OrmError {
            message: message.to_string(),
            cause: Some(cause.to_string()),
        }
    }
}

impl From<OrmError> for AppError {
    fn from(value: OrmError) -> Self {
        AppError {
            message: Some(value.message),
            cause: value.cause,
            error_type: AppErrorType::Database,
        }
    }
}

// impl From<sqlx::Error> for OrmError {
//     fn from(value: sqlx::Error) -> Self {
//         value.
//         match value {
//             sqlx::Error::Configuration(error) => todo!(),
//             sqlx::Error::InvalidArgument(_) => todo!(),
//             sqlx::Error::Database(database_error) => todo!(),
//             sqlx::Error::Io(error) => todo!(),
//             sqlx::Error::Tls(error) => todo!(),
//             sqlx::Error::Protocol(_) => todo!(),
//             sqlx::Error::RowNotFound => todo!(),
//             sqlx::Error::TypeNotFound { type_name } => todo!(),
//             sqlx::Error::ColumnIndexOutOfBounds { index, len } => todo!(),
//             sqlx::Error::ColumnNotFound(_) => todo!(),
//             sqlx::Error::ColumnDecode { index, source } => todo!(),
//             sqlx::Error::Encode(error) => todo!(),
//             sqlx::Error::Decode(error) => todo!(),
//             sqlx::Error::AnyDriverError(error) => todo!(),
//             sqlx::Error::PoolTimedOut => todo!(),
//             sqlx::Error::PoolClosed => todo!(),
//             sqlx::Error::WorkerCrashed => todo!(),
//             sqlx::Error::Migrate(migrate_error) => todo!(),
//             sqlx::Error::InvalidSavePointStatement => todo!(),
//             sqlx::Error::BeginFailed => todo!(),
//             _ => todo!(),
//         }
//     }

// }
