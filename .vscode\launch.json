{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'api-test-1'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=api-test-1",
                    "--package=api-test-1"
                ],
                "filter": {
                    "name": "api-test-1",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'api-test-1'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=api-test-1",
                    "--package=api-test-1"
                ],
                "filter": {
                    "name": "api-test-1",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        }
    ]
}